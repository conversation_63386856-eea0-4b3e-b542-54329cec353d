import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/task_controller.dart';
import 'controllers/auth_controller.dart';

/// ملف اختبار إنشاء المهمة المحددة
class TaskCreationTest {
  
  /// إنشاء المهمة المحددة بالبيانات المطلوبة
  static Future<bool> createSpecificTask() async {
    try {
      // التأكد من تهيئة TaskController
      if (!Get.isRegistered<TaskController>()) {
        Get.put(TaskController(), permanent: true);
      }
      
      final taskController = Get.find<TaskController>();
      
      // البيانات المحددة للمهمة
      final bool success = await taskController.createSpecificTask(
        title: "kk",
        description: "ooo", 
        taskTypeId: 4,
        creatorId: 15,
        assigneeId: null, // unassigned
        departmentId: 2,
        createdAt: 1749061534, // Unix timestamp
        startDate: null,
        dueDate: null,
        completedAt: null,
        status: 1,
        priority: 3,
        completionPercentage: 0,
        estimatedTime: null,
        actualTime: null,
        isDeleted: false,
        accessUserIds: null,
      );
      
      if (success) {
        debugPrint('✅ تم إنشاء المهمة بنجاح');
        debugPrint('📋 عنوان المهمة: kk');
        debugPrint('📝 وصف المهمة: ooo');
        debugPrint('👤 منشئ المهمة: 15');
        debugPrint('🏢 القسم: 2');
        debugPrint('⚡ الأولوية: 3');
        debugPrint('📊 الحالة: 1');
        return true;
      } else {
        debugPrint('❌ فشل في إنشاء المهمة');
        debugPrint('🔍 خطأ: ${taskController.error.value}');
        return false;
      }
      
    } catch (e) {
      debugPrint('💥 خطأ في اختبار إنشاء المهمة: $e');
      return false;
    }
  }
  
  /// اختبار شامل لإنشاء المهمة
  static Future<void> runFullTest() async {
    debugPrint('🚀 بدء اختبار إنشاء المهمة المحددة...');
    debugPrint('=' * 50);
    
    try {
      // التحقق من حالة الاتصال بـ API
      debugPrint('🔗 التحقق من الاتصال بـ API...');
      
      // إنشاء المهمة
      debugPrint('📝 إنشاء المهمة بالبيانات المحددة...');
      final success = await createSpecificTask();
      
      if (success) {
        debugPrint('=' * 50);
        debugPrint('✅ اكتمل الاختبار بنجاح!');
        debugPrint('📋 تم إنشاء المهمة وإضافتها إلى قاعدة البيانات');
      } else {
        debugPrint('=' * 50);
        debugPrint('❌ فشل الاختبار!');
        debugPrint('🔍 يرجى مراجعة الأخطاء أعلاه');
      }
      
    } catch (e) {
      debugPrint('=' * 50);
      debugPrint('💥 خطأ في تشغيل الاختبار: $e');
    }
  }
  
  /// عرض معلومات المهمة المنشأة
  static void showTaskInfo() {
    debugPrint('📋 معلومات المهمة المطلوب إنشاؤها:');
    debugPrint('─' * 40);
    debugPrint('🆔 ID: 0 (سيتم توليده تلقائياً)');
    debugPrint('📝 العنوان: "kk"');
    debugPrint('📄 الوصف: "ooo"');
    debugPrint('🏷️ نوع المهمة: 4');
    debugPrint('👤 المنشئ: 15');
    debugPrint('👥 المعين إليه: null (غير معين)');
    debugPrint('🏢 القسم: 2');
    debugPrint('📅 تاريخ الإنشاء: 1749061534 (Unix timestamp)');
    debugPrint('🚀 تاريخ البدء: null');
    debugPrint('⏰ تاريخ الانتهاء: null');
    debugPrint('✅ تاريخ الإكمال: null');
    debugPrint('📊 الحالة: 1');
    debugPrint('⚡ الأولوية: 3');
    debugPrint('📈 نسبة الإكمال: 0');
    debugPrint('⏱️ الوقت المقدر: null');
    debugPrint('⏲️ الوقت الفعلي: null');
    debugPrint('🗑️ محذوف: false');
    debugPrint('🔐 معرفات المستخدمين للوصول: null');
    debugPrint('─' * 40);
  }
}

/// Widget لاختبار إنشاء المهمة من خلال واجهة المستخدم
class TaskCreationTestWidget extends StatefulWidget {
  const TaskCreationTestWidget({Key? key}) : super(key: key);

  @override
  State<TaskCreationTestWidget> createState() => _TaskCreationTestWidgetState();
}

class _TaskCreationTestWidgetState extends State<TaskCreationTestWidget> {
  bool _isLoading = false;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار إنشاء المهمة'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'بيانات المهمة المطلوب إنشاؤها:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('العنوان: "kk"'),
                    Text('الوصف: "ooo"'),
                    Text('نوع المهمة: 4'),
                    Text('المنشئ: 15'),
                    Text('المعين إليه: null'),
                    Text('القسم: 2'),
                    Text('الحالة: 1'),
                    Text('الأولوية: 3'),
                    Text('نسبة الإكمال: 0'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _createTask,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'إنشاء المهمة',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
            ),
            const SizedBox(height: 20),
            if (_result.isNotEmpty)
              Card(
                color: _result.contains('نجح') ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _result,
                    style: TextStyle(
                      color: _result.contains('نجح') ? Colors.green.shade800 : Colors.red.shade800,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _createTask() async {
    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final success = await TaskCreationTest.createSpecificTask();
      
      setState(() {
        _result = success 
            ? '✅ تم إنشاء المهمة بنجاح!\nالعنوان: "kk"\nالوصف: "ooo"\nالمنشئ: 15\nالقسم: 2'
            : '❌ فشل في إنشاء المهمة. يرجى مراجعة السجلات للحصول على تفاصيل الخطأ.';
      });
    } catch (e) {
      setState(() {
        _result = '💥 خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
