import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/task_controller.dart';
import 'controllers/auth_controller.dart';

/// ملف اختبار إنشاء المهمة المحددة
class TaskCreationTest {
  
  /// إنشاء المهمة المحددة بالبيانات المطلوبة
  static Future<bool> createSpecificTask() async {
    try {
      // التأكد من تهيئة TaskController
      if (!Get.isRegistered<TaskController>()) {
        Get.put(TaskController(), permanent: true);
      }
      
      final taskController = Get.find<TaskController>();
      
      // البيانات المحددة للمهمة
      final bool success = await taskController.createTask(
        title: "kk",
        description: "ooo",
        taskTypeId: 4,
        assigneeId: null, // unassigned
        departmentId: 2,
        priority: 3,
      );
      
      if (success) {
        debugPrint('✅ تم إنشاء المهمة بنجاح');
        debugPrint('📋 عنوان المهمة: kk');
        debugPrint('📝 وصف المهمة: ooo');
        debugPrint('👤 منشئ المهمة: 15');
        debugPrint('🏢 القسم: 2');
        debugPrint('⚡ الأولوية: 3');
        debugPrint('📊 الحالة: 1');
        return true;
      } else {
        debugPrint('❌ فشل في إنشاء المهمة');
        debugPrint('🔍 خطأ: ${taskController.error}');
        return false;
      }
      
    } catch (e) {
      debugPrint('💥 خطأ في اختبار إنشاء المهمة: $e');
      return false;
    }
  }
  
  /// تشخيص وإصلاح مشاكل إنشاء المهام
  static Future<void> diagnoseAndFixIssues() async {
    try {
      // التأكد من تهيئة TaskController
      if (!Get.isRegistered<TaskController>()) {
        Get.put(TaskController(), permanent: true);
      }

      final taskController = Get.find<TaskController>();

      debugPrint('� بدء تشخيص مشاكل إنشاء المهام...');
      debugPrint('=' * 50);

      // تشغيل التشخيص
      final diagnosis = await taskController.diagnoseTaskCreationIssues();

      debugPrint('📊 نتائج التشخيص:');
      debugPrint('⏰ الوقت: ${diagnosis['timestamp']}');
      debugPrint('📈 الحالة العامة: ${diagnosis['overallStatus']}');
      debugPrint('💬 الرسالة: ${diagnosis['message']}');

      final issues = diagnosis['issues'] as List<String>;
      final warnings = diagnosis['warnings'] as List<String>;
      final recommendations = diagnosis['recommendations'] as List<String>;

      if (issues.isNotEmpty) {
        debugPrint('❌ المشاكل المكتشفة:');
        for (int i = 0; i < issues.length; i++) {
          debugPrint('   ${i + 1}. ${issues[i]}');
        }
      }

      if (warnings.isNotEmpty) {
        debugPrint('⚠️ التحذيرات:');
        for (int i = 0; i < warnings.length; i++) {
          debugPrint('   ${i + 1}. ${warnings[i]}');
        }
      }

      if (recommendations.isNotEmpty) {
        debugPrint('💡 التوصيات:');
        for (int i = 0; i < recommendations.length; i++) {
          debugPrint('   ${i + 1}. ${recommendations[i]}');
        }
      }

      // محاولة الإصلاح التلقائي
      if (issues.isNotEmpty) {
        debugPrint('🔧 محاولة الإصلاح التلقائي...');
        final fixResult = await taskController.fixTaskCreationIssues();

        if (fixResult) {
          debugPrint('✅ تم إصلاح المشاكل بنجاح!');
        } else {
          debugPrint('❌ لم يتم إصلاح جميع المشاكل');
        }
      }

      debugPrint('=' * 50);

    } catch (e) {
      debugPrint('� خطأ في التشخيص: $e');
    }
  }

  /// اختبار شامل لإنشاء المهمة
  static Future<void> runFullTest() async {
    debugPrint('🚀 بدء اختبار إنشاء المهمة...');
    debugPrint('=' * 50);

    try {
      // تشخيص المشاكل أولاً
      await diagnoseAndFixIssues();

      // إنشاء المهمة
      debugPrint('📝 إنشاء المهمة بالبيانات المحددة...');
      final success = await createSpecificTask();

      if (success) {
        debugPrint('=' * 50);
        debugPrint('✅ اكتمل الاختبار بنجاح!');
        debugPrint('📋 تم إنشاء المهمة وإضافتها إلى قاعدة البيانات');
      } else {
        debugPrint('=' * 50);
        debugPrint('❌ فشل الاختبار!');
        debugPrint('🔍 يرجى مراجعة الأخطاء أعلاه');
      }

    } catch (e) {
      debugPrint('=' * 50);
      debugPrint('💥 خطأ في تشغيل الاختبار: $e');
    }
  }
  
  /// عرض معلومات المهمة المنشأة
  static void showTaskInfo() {
    debugPrint('📋 معلومات المهمة المطلوب إنشاؤها:');
    debugPrint('─' * 40);
    debugPrint('🆔 ID: 0 (سيتم توليده تلقائياً)');
    debugPrint('📝 العنوان: "kk"');
    debugPrint('📄 الوصف: "ooo"');
    debugPrint('🏷️ نوع المهمة: 4');
    debugPrint('👤 المنشئ: 15');
    debugPrint('👥 المعين إليه: null (غير معين)');
    debugPrint('🏢 القسم: 2');
    debugPrint('📅 تاريخ الإنشاء: 1749061534 (Unix timestamp)');
    debugPrint('🚀 تاريخ البدء: null');
    debugPrint('⏰ تاريخ الانتهاء: null');
    debugPrint('✅ تاريخ الإكمال: null');
    debugPrint('📊 الحالة: 1');
    debugPrint('⚡ الأولوية: 3');
    debugPrint('📈 نسبة الإكمال: 0');
    debugPrint('⏱️ الوقت المقدر: null');
    debugPrint('⏲️ الوقت الفعلي: null');
    debugPrint('🗑️ محذوف: false');
    debugPrint('🔐 معرفات المستخدمين للوصول: null');
    debugPrint('─' * 40);
  }
}

/// Widget لاختبار إنشاء المهمة من خلال واجهة المستخدم
class TaskCreationTestWidget extends StatefulWidget {
  const TaskCreationTestWidget({Key? key}) : super(key: key);

  @override
  State<TaskCreationTestWidget> createState() => _TaskCreationTestWidgetState();
}

class _TaskCreationTestWidgetState extends State<TaskCreationTestWidget> {
  bool _isLoading = false;
  String _result = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار إنشاء المهمة'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'بيانات المهمة المطلوب إنشاؤها:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('العنوان: "kk"'),
                    Text('الوصف: "ooo"'),
                    Text('نوع المهمة: 4'),
                    Text('المنشئ: 15'),
                    Text('المعين إليه: null'),
                    Text('القسم: 2'),
                    Text('الحالة: 1'),
                    Text('الأولوية: 3'),
                    Text('نسبة الإكمال: 0'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _diagnoseIssues,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'تشخيص المشاكل',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _createTask,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      'إنشاء المهمة',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
            ),
            const SizedBox(height: 20),
            if (_result.isNotEmpty)
              Card(
                color: _result.contains('نجح') ? Colors.green.shade50 : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _result,
                    style: TextStyle(
                      color: _result.contains('نجح') ? Colors.green.shade800 : Colors.red.shade800,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _createTask() async {
    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final success = await TaskCreationTest.createSpecificTask();
      
      setState(() {
        _result = success 
            ? '✅ تم إنشاء المهمة بنجاح!\nالعنوان: "kk"\nالوصف: "ooo"\nالمنشئ: 15\nالقسم: 2'
            : '❌ فشل في إنشاء المهمة. يرجى مراجعة السجلات للحصول على تفاصيل الخطأ.';
      });
    } catch (e) {
      setState(() {
        _result = '💥 خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
